import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { WinstonModule } from 'nest-winston';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RouterModule } from 'nest-router';
import { ClusterModule } from '@liaoliaots/nestjs-redis';
/** graphql */
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { DirectiveLocation, GraphQLDirective } from 'graphql';
import { join } from 'path';
/** apollo-config */
import {
  CtripApolloClientModule,
  CtripApolloClientConfig,
  CtripApolloClientService,
} from 'nestjs-ctrip-apollo-client';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { HttpModule } from '@nestjs/axios';
import { AxiosInterceptorService } from 'src/common/interceptor/axios.interceptor';
import { RequestContextModule } from 'nestjs-request-context';

import config, { winstonConfig } from 'src/common/configs';
import { getApolloConfigServiceUrl } from 'src/common/configs/utils';
import { upperDirectiveTransformer } from 'src/common/directives/upper-case.directive';
import { ComplexityPlugin } from 'src/common/plugins/complexity.plugin';

import { DemoModule } from 'src/modules/demo/demo.module';
import { RecipesModule } from 'src/modules/recipes/recipes.module';

import { DemoViewController } from 'src/modules/demo-view/demo-view.controller';
import { AppController } from './app.controller';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true, load: [config] }),
    WinstonModule.forRoot(winstonConfig),
    /**
     * 多数据库配置请参考👇
     * @link https://docs.nestjs.com/techniques/database#multiple-databases
     */
    /**
     * mysql config from `src/common/configs/config.{env}.ts` file
     */
    TypeOrmModule.forRoot(config().mysql as TypeOrmModuleOptions),
    /**
     * mysql config from apollo config
     */
    // TypeOrmModule.forRootAsync({
    //   useFactory: async (
    //     ctripApolloClientService: CtripApolloClientService
    //   ) => {
    //     const mysqlApolloConfig = await ctripApolloClientService.fetchConfigs({
    //       keys: ['username', 'password'],
    //     });
    //     return {
    //       ...config().mysql,
    //       ...mysqlApolloConfig,
    //     } as TypeOrmModuleOptions;
    //   },
    //   inject: [CtripApolloClientService],
    // }),
    ClusterModule.forRoot({
      readyLog: true,
      config: config().redis,
    }),
    RouterModule.forRoutes([
      {
        path: '/api',
        children: [DemoModule],
      },
    ]),
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      autoSchemaFile: join(process.cwd(), 'schema.gql'),
      sortSchema: true,
      transformSchema: (schema) => upperDirectiveTransformer(schema, 'upper'),
      /**
       * enable subscriptions
       * @link https://docs.nestjs.com/graphql/subscriptions#enable-subscriptions-with-apollo-driver
       */
      installSubscriptionHandlers: true,
      buildSchemaOptions: {
        directives: [
          new GraphQLDirective({
            name: 'upper',
            locations: [DirectiveLocation.FIELD_DEFINITION],
          }),
        ],
      },
      playground: config().graphql?.playground,
      path: config().graphql?.path || '/web-assistant/graphql',
    }),
    /**
     * apollo-config
     */
    CtripApolloClientModule.register({
      cachedConfigFilePath: join(process.cwd(), '.apollocache'),
      configServerUrl: getApolloConfigServiceUrl(),
      ...(config().apolloConfig || ({} as CtripApolloClientConfig)),
    }),
    DemoModule,
    RecipesModule,
    HttpModule,
    RequestContextModule,
  ],
  controllers: [AppController, DemoViewController],
  providers: [
    ComplexityPlugin,
    {
      provide: APP_INTERCEPTOR,
      useClass: AxiosInterceptorService,
    },
  ],
})
export class AppModule {}
